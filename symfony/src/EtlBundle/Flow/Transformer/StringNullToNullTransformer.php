<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class StringNullToNullTransformer implements Transformer, LoggerAwareInterface
{
    use LoggerAwareTrait;
    use EtlCommonFunctionsTrait;

    private const STRING_NULL = 'NULL';

    public function __construct(
        private readonly bool $caseSensitive = false
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $this->logger->info('Starting transformer: ' . get_class($this), ['rows' => $rows->count()]);

        $newRows = new Rows();
        /** @var Row $row */
        foreach ($rows as $row) {
            if ($this->isRowFinal($row)) {
                $newRows = $newRows->add($row);
                continue;
            }
            foreach ($row->entries() as $entry) {
                $value = $entry->value();
                if (!is_string($value)) {
                    continue;
                }
                $nullString = $this->caseSensitive ? self::STRING_NULL : strtoupper($value);
                if ($nullString === self::STRING_NULL) {
                    $value = null;
                }
                $row = $row->set($context->entryFactory()->create($entry->name(), $value));
            }
            $newRows = $newRows->add($row);
        }

        $this->logger->info('Stopping transformer: ' . get_class($this), ['rows' => $newRows->count()]);

        return $newRows;
    }
}

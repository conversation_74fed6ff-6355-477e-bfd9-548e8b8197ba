<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Factory\Flow\Transformer;

use LoginAutonom\EtlBundle\Flow\Transformer\StringNullToNullTransformer;
use LoginAutonom\EtlBundle\Interfaces\FlowTransformerFactoryInterface;

final readonly class StringNullToNullTransformerFactory implements FlowTransformerFactoryInterface
{
    private const CASE_SENSITIVE = 'caseSensitive';

    public function build(array $config): object
    {
        $caseSensitive = $config[self::CASE_SENSITIVE] ?? false;
        return new StringNullToNullTransformer(
            $caseSensitive
        );
    }

    public static function getName(): string
    {
        return 'string-null-to-null-transformer';
    }
}

<?php

//  https://innote.login.hu/n-76m528n4

function dbExplain($sql) {
    try {$tb = dbFetchAll("EXPLAIN $sql");}
    catch(\Exception $e) {return "(can't explain, due to parameter binding)";}

    ob_start();debug_print_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
    $btr = ob_get_clean();
    $btr = "#3".string_from($btr,"\n#3");

    $sql = onespace($sql);
    $sql = substr($sql,0,100);
    $out = "\n";
    $out.= "----------------------------------------------------------------\n";
    $out.= "CALL STACK:\n$btr\n";
    $out.= "QUERY:    $sql\n";
    $out.= "EXPLAIN:\n";
    foreach($tb as $x) {
        $key = $x["key"] ? "ok" : "NO INDEX";
        $tbl = $x["table"];
        $out.=sprintf("%12s, %-25s | %s\n",$key,$tbl,$x["extra"]);
    }
    return $out;
}

function dbLogQuery($sql="~",$followBack=2) {

    $logSlow = useExperimental("log slow sql queries");
    $logEach = useExperimental("log all sql queries");
    $logDebug = Yang::session(Yang::DEBUG);
    if(!$logEach && !$logSlow && !$logDebug) return;

    static $prv;
    static $firstCall=1;

    if(!$prv     ) {$prv = microtime(1);}
    if($sql==="~") {$prv = microtime(1);return;}

    $slowQueryLimit = 200;
    $elapsedTime    = round(1000 * (microtime(1) - $prv));
    $isSlowQuery    = !!($elapsedTime > $slowQueryLimit);
    $logThisQuery   = ($logEach) || ($logSlow && $isSlowQuery);
    if (!$logThisQuery && !$logDebug) {
        return;
    }

    $sql = preg_replace("/\s+/"," ",$sql); if(string_begins($sql,"EXPLAIN ")) return;
    $now = date("Y-m-d H:i:s");
    $mic = substr(explode(" ",microtime())[0],2,3);
    $dif = sprintf("%5d",$elapsedTime);
    $loc = codeLocation($followBack);
    $loc = sprintf("%s:%s",$loc["file"],$loc["line"]);
    $url = $_SERVER['REQUEST_URI'];
    $dash = '--------------------------------';

    if($logDebug) {
        Yang::log("\n url: $url \n $dash \n $dif \t $now . $mic \t $loc \n $sql \n", Yang::LOGLEVEL_LOG);
    }
    if(!$logThisQuery) {
        return;
    }

    $logPath = dirname(__DIR__)."/runtime";
    if(is_writable("d:/debug")) $logPath = "d:/debug";

    $slow = $isSlowQuery;
    if($firstCall) {
        if(  1  ) file_put_contents("$logPath/sql.all.txt"  ,"\n\n$url\n$dash\n",FILE_APPEND);
        if(  1  ) file_put_contents("$logPath/sql.last.txt" ,"\n\n$url\n$dash\n");
        if($slow) file_put_contents("$logPath/sql.slow.txt" ,"\n\n$url\n$dash\n",FILE_APPEND);
    }

    if(  1  ) file_put_contents("$logPath/sql.all.txt"  ,"$dif\t$now.$mic\t$loc\t$sql\n",FILE_APPEND);
    if(  1  ) file_put_contents("$logPath/sql.last.txt" ,"$dif\t$now.$mic\t$loc\t$sql\n",FILE_APPEND);
    if($slow) file_put_contents("$logPath/sql.slow.txt" ,"$dif\t$now.$mic\t$loc\t$sql\n",FILE_APPEND);

    $firstCall = 0;
    $prv = microtime(1);

    if(!useExperimental("explain slow sql queries")) return;

    $explain = dbExplain($sql);
    file_put_contents("$logPath/sql.explain.txt","$explain\n",FILE_APPEND);

    $prv = microtime(1);

}


function dbExecute($sql) {
    dbLogQuery();       $out = Yii::app()->db->createCommand($sql)->execute();
    dbLogQuery($sql);
    return $out;
}

function dbFetchAll($sql,$k="",$v="") {
    dbLogQuery(); $a = Yii::app()->db->createCommand($sql)->queryAll();
    dbLogQuery($sql);
    if(!$k) return $a;
    $o = []; foreach($a as $r) $o[$r[$k]]=($v ? $r[$v] : $r);
    return $o;
}

function dbFetchRow($sql) {
    dbLogQuery(); $o = Yii::app()->db->createCommand($sql)->queryRow();
    dbLogQuery($sql);
    return $o;
}

function dbFetchValue($sql,$index=null) {
    dbLogQuery();
    switch(isset($index)?1:0) {
        case 0: { $out = Yii::app()->db->createCommand($sql)->queryScalar(); break; }
        case 1: 
			{
				!is_null($index) && !is_array($index) ? $index = [] : $index;
				$out = Yii::app()->db->createCommand($sql)->queryScalar($index);
				break;
			}
    }
    dbLogQuery($sql);
    return $out;
}

function dbFetchColumn($sql) {
    dbLogQuery(); $out = Yii::app()->db->createCommand($sql)->queryColumn();
    dbLogQuery($sql);
    return $out;
}

function dbTryFetchAll($sql,$path) {
    $resultFile = "$path/dbTryFetchAll.results.txt";
    try{

        $x = dbFetchAll($sql);
        file_put_contents($resultFile,print_r($x,1));

    }catch(\Exception $e) {

        $sqlError = $e->getMessage();
        $sqlError = explode("]: ",$sqlError);
        $sqlError = array_pop($sqlError);
        file_put_contents($resultFile,
            "SQL said:\n\n".
            "$sqlError\n\n\n\n".
            "Your SQL query was:\n\n$sql"
        );

    }
}

function dbLastInsertID() {
    return Yii::app()->db->getLastInsertID();
}

// function dbError() {
//     return Yii::app()->db->createCommand($sql)->queryAll();
// }

function dbFetch($sql, $function = "queryAll", $key="", $value="")
{
    dbLogQuery();
    $dbResult = Yii::app()->db->createCommand($sql)->$function();

    if (!$key) return $dbResult;

    $result = [];
    foreach ($dbResult as $row)
    {
        $result[$row[$key]] = ($value ? $row[$value] : $row);
    }
    dbLogQuery($sql);
    return $result;
}

function dbConnectionString() {
    return Yii::app()->db->connectionString;
}

function dbMultiInsert($table,$data=[]) {

    if(!is_array($data))    return 'dbMultiInsert: $data is not an array';
    if(!count($data))       return 'dbMultiInsert: $data contains no rows';

    dbLogQuery();
    $out = "OK";
    try{
        $builder = Yii::app()->db->schema->commandBuilder;
        $command = $builder->createMultipleInsertCommand($table, $data);
        $success = $command->execute();
        if(!$success) $out = 'dbMultiInsert: sql execute failed';
    }catch (Exception $e){
        $out = $e->getMessage();
    }
    dbLogQuery("dbMultiInsert into $table");

    return $out;

}

function dbQuoteValue($s) {
    return Yii::app()->db->quoteValue($s);
}

function dbGetTableNames() {
    dbLogQuery(); $out = Yii::app()->db->schema->getTableNames();
    dbLogQuery(__FUNCTION__);
    return $out;
}

function dbGetTable($name) {
    dbLogQuery(); $out = Yii::app()->db->schema->getTable($name);
    dbLogQuery(__FUNCTION__."($name)");
    return $out;
}

function dbGetTableColumns($name) {
    dbLogQuery(); $out = Yii::app()->db->schema->getTable($name)->columns;
    dbLogQuery(__FUNCTION__."($name)");
    return $out;
}

function getErrorsFromModel($model) {
	$errorString = "";
	$modelName = get_class($model);
	$errors = $model->getErrors();
	$errors = Yang::arrayMerge($errors, $model->getJsonColumnValidateError());

	foreach ($errors as $key => $error) {

		$e = Dict::getValue($error[0], ['attribute' => $model->getAttributeLabel($key)]);
		$errorString .= (empty($e) ? $error[0] : $e) . "<BR>";
	}

	if (!empty($errorString)) {
		$errorString = "<BR>" . $errorString;
	}

	return $errorString;
}
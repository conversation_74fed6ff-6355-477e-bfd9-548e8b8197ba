<?php

class AssociationOfCompetency
{
	private static $publishedStatus = Status::PUBLISHED;

	public static function getAllParent($table, $id)
	{
		$hiearchy = explode(';', App::getSetting('group_hierarchy'));
		$index = array_search($table, $hiearchy);
		$parentIds = [];
		$actualId = $id;
		$actualTable = $table;
		$tableIds = self::getTableIds();

		while ($index > 0)
		{
			$tableData = $tableIds[$actualTable];
			$sql = "SELECT 
						parent
					FROM
						{$actualTable}
					WHERE
							status = " . self::$publishedStatus . "
						AND " . $tableData['id'] . " = '{$actualId}'	
					";
			$parent = dbFetchValue($sql);

			$parentIds[$actualTable] = $actualId;
			$actualId = $parent;
			$index--;
			$actualTable = $hiearchy[$index];
		}

		return $parentIds;
	}

	public static function getAllParentCompetency($table, $id)
	{
		$parents = self::getAllParent($table, $id);
		$where = "";
		foreach ($parents as $parentTable => $parentId) {
			$where .= "OR (cl.`link_type` = '{$parentTable}' AND cl.`link_table_id` = '{$parentId}') ";
		}

		$result = [];
		if (!empty($where))
		{
			$sql = "SELECT
						c.competency_id,
						c.competency_name,
						cl.competency_level,
						cg.`has_simplified_training`,
						cg.competency_group_name
					FROM competency_links cl
					LEFT JOIN competency c ON
							c.competency_id = cl.competency_id
						AND c.status = " . self::$publishedStatus . "
					LEFT JOIN competency_group cg ON
							cg.competency_group_id = c.competency_group_id
						AND cg.status = " . self::$publishedStatus . "
					WHERE
						0 {$where}
			";
			$result = dbFetchAll($sql);
		}

		return $result;
	}

	public static function getTableIds(string $extraCondition = "1=1")
	{
		$sql = "
			SELECT
				related_table AS tbl, 
				id_in_related_table AS id,
				group_name_column_in_related_table AS name,
				related_id
			FROM link_group_to_terminal_group_id_table_relations
			WHERE
					status = " . self::$publishedStatus . "
				AND {$extraCondition}
		";

		return dbFetchAll($sql, 'tbl');
	}

	public static function getCompetencyByTreeId($idArray, $parentCompentecies_filter = "", $sortable1_filter = "", $onlyParent = false, $parentCompentecies_group_select = null, $associatedCompetencies_group_select = null)
	{
		$result = [];

		$result['parent'] = self::getCompetencyById($idArray, $parentCompentecies_filter, true, 0, $parentCompentecies_group_select);
		
		if (!$onlyParent)
		{
			$result['existing'] = self::getCompetencyById($idArray, $sortable1_filter, false, 0, $associatedCompetencies_group_select);
		}

		return $result;
	}

	public static function getCompetencyById($idArray, $filter = '', $getParent = false, $groupHierarchyMode = 0, $group_select = null, $disableLastChild = true)
	{
		$groupHierarchy = explode(";", App::getSetting('group_hierarchy'));

		$where = "1";
		$end = count($idArray);
		
		if ($getParent && $disableLastChild) {
			$end--;
		}

		for ($i = 0; $i < $end; $i++) {
            if ($groupHierarchyMode == 0) {
                $where .= ' AND (tree_level->>"$.' . $groupHierarchy[$i] . '" = "' . $idArray[$i] . '"';
            }
            else {
                $where .= ' AND (tree_level->>"$.' . $groupHierarchy[$i] . '" = "' . $idArray[$groupHierarchy[$i]] . '"';
            }
			if ($getParent || (!$getParent && $i < $end-1)) {
				$where .= ' OR JSON_EXTRACT(tree_level, "$.' . $groupHierarchy[$i] . '") IS NULL ';
			}
			$where .= ")";
		}

		for ($j = $i; $j < count($groupHierarchy); $j++) {
			$where .= ' AND JSON_EXTRACT(tree_level, "$.' . $groupHierarchy[$j] . '") IS NULL';
		}

        if ($group_select !== null && $group_select !== "") {
            $filter_group = "AND comp.`competency_group_id` = '" . $group_select . "'";
        }

		if ($filter !== null && $filter !== "") {
			$filter_competency_name = "AND comp.`competency_name` LIKE '%{$filter}%'";
		}

		$sql = "SELECT
					cr.row_id,
					IF(DATE(comp.created_on) >= CURDATE() - INTERVAL 1 WEEK, 1, 0) as is_new,
					comp.competency_id,
					comp.competency_name,
					MAX(cr.`level`) AS competency_level,
					comp.`competency_group_id` AS competency_group_id,
					cg.`has_simplified_training`
				FROM competency_requirement cr
				LEFT JOIN competency comp ON
						comp.competency_id = cr.competency_id
					AND comp.status = " . self::$publishedStatus . "
					AND comp.`validity` IS NULL
				LEFT JOIN `competency_group` cg ON
						cg.`competency_group_id` = comp.`competency_group_id`
					AND cg.`status` = " . self::$publishedStatus . "
				WHERE
					{$where}
					AND cr.status = " . self::$publishedStatus . "
					" . $filter_competency_name . "
					" . $filter_group . "
					AND comp.`competency_id` IS NOT NULL
				GROUP BY comp.competency_id
				ORDER BY comp.competency_name";

		return dbFetchAll($sql);
	}

	public static function getSqlSelectAndJoin($groupHierarchy, $validFrom, $validTo)
	{
		$groups = [
			'company_org_group1_id' => 'e',
			'company_org_group2_id' => 'e',
			'company_org_group3_id' => 'e',
			'unit_id'				=> 'e',
			'workgroup_id'			=> 'ec',
			'employee_position_id'	=> 'ec',
		];

		$selectSql = "";
		$joinSql = "";

		foreach ($groups as $col => $table)
		{
			if (in_array(substr($col, 0, -3), $groupHierarchy))
			{
				if (EmployeeGroupConfig::isActiveGroup($col)) {
					$joinSql .= EmployeeGroup::getLeftJoinSQLWithoutCal($col, "ec", null, "", "e", $validFrom, $validTo);
					$selectSql .= "employee_group_{$col}.`group_value` AS " . substr($col, 0, -3) . ", ";
				} else {
					$selectSql .= "{$table}.{$col} AS " . substr($col, 0, -3) . ", ";
				}
			}
		}

		if(EmployeeGroupConfig::isActiveGroup('job_task_id'))
		{
			$joinSql .= EmployeeGroup::getLeftJoinSQLWithoutCal("job_task_id","ec", null, "", "e", $validFrom, $validTo);
			$selectSql .= "employee_group_job_task_id.`group_value` AS job_tasks, ";
		}
		$selectSql = substr($selectSql, 0, -2);

		return ['select' => $selectSql, 'join' => $joinSql];
	}

	public static function getHierarchyGroups($groupHierarchy, $employeeData)
	{
		$groups = [];
		foreach ($groupHierarchy as $g) {
			$groups[$g] = $employeeData[array_key_first($employeeData)][$g];
		}

		return self::getCompetencyById($groups, '', true, true);
	}

	public static function getParentCompetenciesByEcId($ecId)
	{
		$groupHierarchy = explode(";", App::getSetting('group_hierarchy'));

		$extraSqls = self::getSqlSelectAndJoin($groupHierarchy, 'ec.valid_from', 'ec.valid_to');
		$defaultEnd = App::getSetting("defaultEnd");
		$result = [];

		if (!empty($extraSqls['select'])) {
			$sql = "
				SELECT
					e.company_id AS company,
					" . $extraSqls['select'] . ",
					ec.employee_contract_id
				FROM employee_contract ec
				LEFT JOIN `employee` e ON
						e.`employee_id` = ec.`employee_id`
					AND e.`status` = " . self::$publishedStatus . "
					AND CURDATE() BETWEEN e.valid_from AND e.valid_to
				" . $extraSqls['join'] . "
				WHERE
						ec.employee_contract_id = '{$ecId}'
					AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, '{$defaultEnd}')
					AND CURDATE() BETWEEN ec.ec_valid_from AND IFNULL(ec.ec_valid_to, '{$defaultEnd}')
			";

			$employeeData = dbFetchAll($sql);

			$result = self::getCompetencyById($employeeData[0], '', true, true);
		}

		return $result;
	}

	public static function getAllAssociatedCompetenciesInGroup(array $idArray): array
	{
		$groupHierarchy = explode(";", App::getSetting('group_hierarchy'));

		$andWhere = "1";
		$orWhere = "";

		$results = [];

		for ($i = 0; $i < count($idArray); $i++)
		{
			$andWhere .= ' AND ((tree_level->>"$.' . $groupHierarchy[$i] . '" = "' . $idArray[$i] . '")';

			if ($i > 0) {
				$orWhere .= ') OR ((tree_level->>"$.' . $groupHierarchy[$i-1] . '" = "' . $idArray[$i-1] . '") AND JSON_EXTRACT(tree_level, "$.' . $groupHierarchy[$i] . '") IS NULL) ';
			} else {
				$andWhere .= ')';
			}
		}

		$SQL = "SELECT
					cr.tree_level,
					IF(DATE(comp.created_on) >= CURDATE() - INTERVAL 1 WEEK, 1, 0) as is_new,
					comp.competency_id,
					comp.competency_name,
					comp.`competency_group_id` AS competency_group_id,
					cg.`has_simplified_training`
				FROM competency_requirement cr
				LEFT JOIN competency comp ON
						comp.competency_id = cr.competency_id
					AND comp.status = " . self::$publishedStatus . "
					AND comp.`validity` IS NULL
				LEFT JOIN `competency_group` cg ON
						cg.`competency_group_id` = comp.`competency_group_id`
					AND cg.`status` = " . self::$publishedStatus . "
				WHERE
						{$andWhere}
						{$orWhere}
					AND cr.status = " . self::$publishedStatus . "
					AND comp.`competency_id` IS NOT NULL
				GROUP BY cr.tree_level, comp.competency_id
				ORDER BY comp.competency_name";

		$results = dbFetchAll($SQL);

		return $results;
	}
}
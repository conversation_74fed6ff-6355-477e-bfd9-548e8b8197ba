<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\FlowContext;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\UnitTableFieldsEnum;


final readonly class NewCompanyOrgGroupToModelsTransformer implements Transformer
{
    public function __construct(
        private int $companyOrgGroupNumber,
        private string $cacheName,
        private string $newCacheName,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $rowsArray = iterator_to_array($context->cache()->read($this->cacheName));
        /** @var Rows $payrollRows */
        $payrollRows = reset($rowsArray);
        $unitArrays = $payrollRows[0]->toArray();
        $models = [];
        foreach ($unitArrays as $key => $values) {
            $models[] = match($this->companyOrgGroupNumber)
            {
                1 => $this->setCompanyOrgGroup1Model($key, $values),
                2 => $this->setCompanyOrgGroup2Model($key, $values),
                3 => $this->setCompanyOrgGroup3Model($key, $values)
            };

        }

        $rowLines[] = $this->rowFactory->create($models);
        $context->cache()->add($this->newCacheName, new Rows(...$rowLines));

        return $rows;
    }

    private function setCompanyOrgGroup1Model($key, $values) : \CompanyOrgGroup1
    {
        $model = new \CompanyOrgGroup1();
        $model->company_org_group_id = $key;
        $model->company_id = UnitTableFieldsEnum::COMPANY_ID_ALL;
        $model->payroll_id = UnitTableFieldsEnum::PAYROLL_ID_ALL;
        $model->company_org_group_name = $values[LoadGetOrgUnit3ToGroupTransformer::NAME];
        $model->identifier = $values[LoadGetOrgUnit3ToGroupTransformer::IDENTIFIER];
        $model->minimal_group_count = 0;
        $model->status = \Status::PUBLISHED;
        $model->valid_from = UnitTableFieldsEnum::VALID_FROM;
        $model->valid_to = UnitTableFieldsEnum::VALID_TO;
        $model->created_by = UnitTableFieldsEnum::CREATED_BY;
        $model->created_on = (new \DateTime(UnitTableFieldsEnum::CREATED_ON))->format("Y-m-d");
        return $model;
    }

    private function setCompanyOrgGroup2Model($key, $values) : \CompanyOrgGroup2
    {
        $model = new \CompanyOrgGroup2();
        $model->company_org_group_id = $key;
        $model->company_id = UnitTableFieldsEnum::COMPANY_ID_ALL;
        $model->payroll_id = UnitTableFieldsEnum::PAYROLL_ID_ALL;
        $model->company_org_group_name = $values[LoadGetOrgUnit3ToGroupTransformer::NAME];
        $model->identifier = $values[LoadGetOrgUnit3ToGroupTransformer::IDENTIFIER];
        $model->minimal_group_count = 0;
        $model->status = \Status::PUBLISHED;
        $model->valid_from = UnitTableFieldsEnum::VALID_FROM;
        $model->valid_to = UnitTableFieldsEnum::VALID_TO;
        $model->created_by = UnitTableFieldsEnum::CREATED_BY;
        $model->created_on = (new \DateTime(UnitTableFieldsEnum::CREATED_ON))->format("Y-m-d");
        return $model;
    }

    private function setCompanyOrgGroup3Model($key, $values) : \CompanyOrgGroup3
    {
        $model = new \CompanyOrgGroup3();
        $model->company_org_group_id = $key;
        $model->company_id = UnitTableFieldsEnum::COMPANY_ID_ALL;
        $model->payroll_id = UnitTableFieldsEnum::PAYROLL_ID_ALL;
        $model->company_org_group_name = $values[LoadGetOrgUnit3ToGroupTransformer::NAME];
        $model->identifier = $values[LoadGetOrgUnit3ToGroupTransformer::IDENTIFIER];
        $model->minimal_group_count = 0;
        $model->status = \Status::PUBLISHED;
        $model->valid_from = UnitTableFieldsEnum::VALID_FROM;
        $model->valid_to = UnitTableFieldsEnum::VALID_TO;
        $model->created_by = UnitTableFieldsEnum::CREATED_BY;
        $model->created_on = (new \DateTime(UnitTableFieldsEnum::CREATED_ON))->format("Y-m-d");
        return $model;
    }

    public function __serialize(): array
    {
        return [
            'companyOrgGroupNumber' => $this->companyOrgGroupNumber,
            'cacheName' => $this->cacheName,
            'newCacheName' => $this->newCacheName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->companyOrgGroupNumber = $data['companyOrgGroupNumber'];
        $this->cacheName = $data['cacheName'];
        $this->newCacheName = $data['newCacheName'];
    }
}

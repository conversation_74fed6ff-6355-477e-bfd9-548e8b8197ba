<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Components\NexonApi\Enum\NexonFieldsEnum;
use Components\NexonApi\Provider\CompanyFromDbProvider;
use Components\NexonApi\Provider\CostCenterFromDbProvider;
use Components\NexonApi\Provider\CostFromDbProvider;
use Components\NexonApi\Provider\UnitFromDbProvider;
use Components\NexonApi\Provider\PayrollFromDbProvider;
use Components\NexonApi\Provider\CompanyOrgGroup1FromDbProvider;
use Components\NexonApi\Provider\CompanyOrgGroup2FromDbProvider;
use Components\NexonApi\Provider\CompanyOrgGroup3FromDbProvider;

use Flow\ETL\FlowContext;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;

final readonly class LoadGetOrgUnit3ToGroupTransformer implements Transformer
{
    public const NAME = 'name';
    public const IDENTIFIER = 'identifier';

    public function __construct(
        private string $providerName,
        private array $tableColumnsEnum,
        private int $nexonLevel,
        private string $cacheName,
        private string $newTableDataCache,
        private string $allTableDataCache,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory(),
        private UnitFromDbProvider $unitFromDbProvider = new UnitFromDbProvider(),
        private CompanyFromDbProvider $companyFromDbProvider = new CompanyFromDbProvider(),
        private CostFromDbProvider $costFromDbProvider = new CostFromDbProvider(),
        private CostCenterFromDbProvider $costCenterFromDbProvider = new CostCenterFromDbProvider(),
        private PayrollFromDbProvider $payrollFromDbProvider = new PayrollFromDbProvider(),
        private CompanyOrgGroup1FromDbProvider $companyOrgGroup1FromDbProvider = new CompanyOrgGroup1FromDbProvider(),
        private CompanyOrgGroup2FromDbProvider $companyOrgGroup2FromDbProvider = new CompanyOrgGroup2FromDbProvider(),
        private CompanyOrgGroup3FromDbProvider $companyOrgGroup3FromDbProvider = new CompanyOrgGroup3FromDbProvider(),
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $fromDb = $this->provideTableDatas();

        $rowsArray = iterator_to_array($context->cache()->read($this->cacheName));
        /** @var Rows $getOrgUnits3Rows */
        $getOrgUnits3Rows = reset($rowsArray);
        $getOrgUnits3RowsArrays = $getOrgUnits3Rows[0]->toArray();
        $newData = [];
        $allData = $this->localDataAndGetOrgUnits3Merge($fromDb, $getOrgUnits3RowsArrays, $newData, $this->tableColumnsEnum, $this->nexonLevel);

        $rowNewLines[] = $this->rowFactory->create($newData);
        $context->cache()->add($this->newTableDataCache, new Rows(...$rowNewLines));

        $rowAllLines[] = $this->rowFactory->create($allData);
        $context->cache()->add($this->allTableDataCache, new Rows(...$rowAllLines));

        return $rows;
    }

    private function localDataAndGetOrgUnits3Merge(array $datas, array $getOrgUnits3, array &$newData, array $tabledProperties, int $nexonLevel): array
    {
        $allData = [];

        foreach ($datas as $data) {
            $allData[$data->{$tabledProperties['id']}] = $data->{$tabledProperties['name']};
        }

        foreach ($getOrgUnits3 as $getOrgUnits) {
            if ($getOrgUnits[0][NexonFieldsEnum::LEVEL] != $nexonLevel) {
                continue;
            }

            $name = $getOrgUnits[0][NexonFieldsEnum::NAME][0][NexonFieldsEnum::VALUE];
            $identifier = !empty($getOrgUnits[0][NexonFieldsEnum::CODE]) ?
                $getOrgUnits[0][NexonFieldsEnum::CODE] : null;

            if (in_array($name, $allData)) {
                continue;
            }

            $id = \App::getIncreasedGlobalIdentifier($tabledProperties['id']);

            $allData[$id] = [
                self::NAME => $name,
                self::IDENTIFIER => $identifier
            ];

            $newData[$id] = [
                self::NAME => $name,
                self::IDENTIFIER => $identifier
            ];
        }

        return $allData;
    }

    private function provideTableDatas()
    {
        return match($this->providerName)
        {
            'UnitFromDbProvider'  => ($this->unitFromDbProvider)(),
            'CompanyFromDbProvider'  => ($this->companyFromDbProvider)(),
            'CostFromDbProvider' => ($this->costFromDbProvider)(),
            'CostCenterFromDbProvider' => ($this->costCenterFromDbProvider)(),
            'PayrollFromDbProvider' => ($this->payrollFromDbProvider)(),
            'CompanyOrgGroup1FromDbProvider' => ($this->companyOrgGroup1FromDbProvider)(),
            'CompanyOrgGroup2FromDbProvider' => ($this->companyOrgGroup2FromDbProvider)(),
            'CompanyOrgGroup3FromDbProvider' => ($this->companyOrgGroup3FromDbProvider)(),
        };
    }

    public function __serialize(): array
    {
        return [
            'providerName' => $this->providerName,
            'tableColumnsEnum' => $this->tableColumnsEnum,
            'nexonLevel' => $this->nexonLevel,
            'cacheName' => $this->cacheName,
            'newTableDataCache' => $this->newTableDataCache,
            'allTableDataCache' => $this->allTableDataCache
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->providerName = $data['providerName'];
        $this->tableColumnsEnum = $data['tableColumnsEnum'];
        $this->nexonLevel = $data['nexonLevel'];
        $this->cacheName = $data['cacheName'];
        $this->newTableDataCache = $data['newTableDataCache'];
        $this->allTableDataCache = $data['allTableDataCache'];
    }
}

<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\FlowContext;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\UnitTableFieldsEnum;

final readonly class NewPayrollsToModelsTransformer implements Transformer
{
    public function __construct(
        private string          $cacheName,
        private string          $newCacheName,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $rowsArray = iterator_to_array($context->cache()->read($this->cacheName));
        /** @var Rows $payrollRows */
        $payrollRows = reset($rowsArray);
        $unitArrays = $payrollRows[0]->toArray();
        $models = [];
        foreach ($unitArrays as $id => $values) {
            $model = new \Payroll();
            $model->payroll_id = $id;
            $model->company_id = UnitTableFieldsEnum::COMPANY_ID_ALL;
            $model->payroll_name = $values[LoadGetOrgUnit3ToGroupTransformer::NAME];
            $model->identifier = $values[LoadGetOrgUnit3ToGroupTransformer::IDENTIFIER];
            $model->status = \Status::PUBLISHED;
            $model->valid_from = UnitTableFieldsEnum::VALID_FROM;
            $model->valid_to = UnitTableFieldsEnum::VALID_TO;
            $model->created_by = UnitTableFieldsEnum::CREATED_BY;
            $model->created_on = (new \DateTime(UnitTableFieldsEnum::CREATED_ON))->format("Y-m-d");
            $models[] = $model;
        }

        $rowLines[] = $this->rowFactory->create($models);
        $context->cache()->add($this->newCacheName, new Rows(...$rowLines));

        return $rows;
    }

    public function __serialize(): array
    {
        return [
            'cacheName' => $this->cacheName,
            'newCacheName' => $this->newCacheName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->cacheName = $data['cacheName'];
        $this->newCacheName = $data['newCacheName'];
    }
}

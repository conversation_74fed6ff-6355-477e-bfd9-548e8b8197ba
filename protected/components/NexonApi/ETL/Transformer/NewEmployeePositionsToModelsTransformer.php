<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\FlowContext;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\EmployeePositionFieldsEnum;


final readonly class NewEmployeePositionsToModelsTransformer implements Transformer
{
    public function __construct(
        private string          $cacheName,
        private string          $newCacheName,
        private ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $rowsArray = iterator_to_array($context->cache()->read($this->cacheName));
        /** @var Rows $employeePositionRows */
        $employeePositionRows = reset($rowsArray);
        $employeePositionArrays = $employeePositionRows[0]->toArray();
        $models = [];
        foreach ($employeePositionArrays as $key => $values) {
            $model = new \EmployeePosition();
            $model->employee_position_id = $key;
            $model->employee_position_name = $values[LoadGetOrgUnit3ToGroupTransformer::NAME];
            $model->status = \Status::PUBLISHED;
            $model->valid_from = EmployeePositionFieldsEnum::VALID_FROM;
            $model->valid_to = EmployeePositionFieldsEnum::VALID_TO;
            $model->created_by = EmployeePositionFieldsEnum::CREATED_BY;
            $model->created_on = (new \DateTime(EmployeePositionFieldsEnum::CREATED_ON))->format("Y-m-d");
            $models[] = $model;
        }

        $rowLines[] = $this->rowFactory->create($models);
        $context->cache()->add($this->newCacheName, new Rows(...$rowLines));

        return $rows;
    }

    public function __serialize(): array
    {
        return [
            'cacheName' => $this->cacheName,
            'newCacheName' => $this->newCacheName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->cacheName = $data['cacheName'];
        $this->newCacheName = $data['newCacheName'];
    }
}

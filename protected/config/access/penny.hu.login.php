<?php
	$module_name = 'epp_software_name-main';

	$module = ['ttwa-ahp-core', 'ttwa-ahp', 'ttwa-wfm', 'ttwa-csm-core', 'ttwa-csm', 'ttwa-SchedulingAssistant'];

	$host = 'localhost';
	$user = 'dbauser';
	$pass = 'V1aifSphFeSr';
	$db = 'penny_ease_login_test';

	$reportDbName = 'ttwa_jasper';
	$reportDir = 'c:/login/ttwa/webroot/reports/';
	$nodeReportDir = 'c:/login/ttwa/webroot/reports/';
	$isHttpsLinks = false;
	
	$customerDbPatchName = "penny";
	
	$greenDesign = true;

	$email_settings =
	[
		'host'				=> 'smtp.gmail.com',
		'port'				=> '465',
		'is_smtp'			=> true,
		'smtp_auth'			=> true,
		'smtp_secure'		=> 'ssl',
		'username'			=> '<EMAIL>',
		'password'			=> 'pvirgbjwykxizgdg',
		'is_html'			=> true,
		'sender'			=> '<EMAIL>',
		'from_email'		=> '<EMAIL>',
		'from_name'			=> 'Login Autonom Teszt',
		'reply_to'			=> '<EMAIL>',
		'confirm_reading'	=> false,
		'charset'			=> 'UTF-8',
	];
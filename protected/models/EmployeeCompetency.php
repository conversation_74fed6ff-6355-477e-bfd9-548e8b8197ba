<?php

'yii2-only`;

	namespace app\models;
	use app\components\ActiveRecords\MyHistoryActiveRecord;
	use app\components\App;
	use app\components\Dict;
	use app\models\Status;
	use Yang;

`/yii2-only';


/**
 * This is the model class for table "employee_competency".
 *
 * The followings are the available columns in table 'employee_competency':
 * @property string $row_id
 * @property string $pre_row_id
 * @property string $employee_contract_id
 * @property string $competency_id
 * @property string $competency_group_id
 * @property string $fs_file_id
 * @property string $level_id
 * @property string $result
 * @property integer $order
 * @property string $note
 * @property string $valid_from
 * @property string $valid_to
 * @property integer $status
 * @property string $created_by
 * @property string $created_on
 * @property string $modified_by
 * @property string $modified_on
 */
class EmployeeCompetency extends MyHistoryActiveRecord
{
	private static $fileUploadField;
	private static $publishedStatus = Status::PUBLISHED;
	private static $draftStatus = Status::DRAFT;
	private $competencyOrderParams;
    private $isPriorityCompetencyFromContract;

	public function __construct($scenario = 'insert')
	{
		parent::__construct($scenario);
		if ($scenario === 'insert') {
			$this->valid_from = date('Y-m-d');
			$this->status = self::$publishedStatus;
		}

		$this->identifyColumn = "employee_contract_id";
		$this->autoSplitIntervals = true;
		$this->canDeleteLast = true;
        $this->competencyOrderParams = (array)json_decode(App::getSetting('showEmployeeCompetencyOrder'));
        $this->autoSplitColumns = ((int)$this->competencyOrderParams['show_order'] === 1) ? ["employee_contract_id", "competency_id", "order"] : ["employee_contract_id", "competency_id",];
        $this->historyValidationColumns = ((int)$this->competencyOrderParams['show_order'] === 1) ? [["employee_contract_id", "competency_id", "order"],] : [["employee_contract_id", "competency_id",],];
		self::$fileUploadField = App::getSetting("competencyTabFileUpload");
        $this->setIsPriorityCompetencyFromContract(false);
	}
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'employee_competency';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
            $required = 'employee_contract_id, competency_id, level_id, valid_from, status, created_by, created_on' 
                        . ((int)App::getSetting('showCompetencyGroup') === 1 ? ', competency_group_id' : '');
			$array =  [
					[$required, 'required', 'message'=>"error_field_required"],
					['status, order', 'numerical', 'integerOnly'=>true],
					['pre_row_id, order', 'length', 'max'=>11],
					['employee_contract_id, competency_id, competency_group_id, level_id, created_by, modified_by', 'length', 'max'=>32],
					['note', 'length', 'max'=>512],
					['result, valid_to, modified_on', 'safe'],
				];
				if (self::$fileUploadField == '1')
				{
					$array[] = ['row_id, pre_row_id, employee_contract_id, competency_id, fs_file_id, level_id, order, result, note, valid_from,
								valid_to, status, created_by, created_on, modified_by, modified_on', 'safe', 'on'=>'search'];
					$array[] = ['employee_contract_id, competency_id, fs_file_id, level_id, created_by, modified_by', 'length', 'max'=>32];
				}
				else
				{
					$array[] = ['row_id, pre_row_id, employee_contract_id, competency_id, level_id, order, result, note, valid_from,
						valid_to, status, created_by, created_on, modified_by, modified_on', 'safe', 'on'=>'search'];
					$array[] = ['employee_contract_id, competency_id, level_id, created_by, modified_by', 'length', 'max'=>32];
				}

			return $array;
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return [];
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		$array = [
			'row_id'				=> Dict::getValue("Line"),
			'pre_row_id'			=> Dict::getValue("pre_row_id"),
			'employee_contract_id'	=> Dict::getValue("employee_contract_id"),
			'competency_id'			=> Dict::getValue("competency_id"),
			'competency_group_id'	=> Dict::getValue("competency_group_id"),
			'level_id'				=> Dict::getValue("level_id"),
			'result'				=> Dict::getValue("result"),
			'order'		            => Dict::getValue("order"),
			'note'					=> Dict::getValue("note"),
			'valid_from'			=> Dict::getValue("valid_from"),
			'valid_to'				=> Dict::getValue("valid_to"),
			'status'				=> Dict::getValue("status"),
			'created_by'			=> Dict::getValue("created_by"),
			'created_on'			=> Dict::getValue("created_on"),
			'modified_by'			=> Dict::getValue("modified_by"),
			'modified_on'			=> Dict::getValue("modified_on"),
		];
		if (self::$fileUploadField == '1')
		{
			$array['fs_file_id']	= Dict::getValue("file");
		}
		return $array;
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('row_id',$this->row_id,true);
		$criteria->compare('pre_row_id',$this->pre_row_id,true);
		$criteria->compare('employee_contract_id',$this->employee_contract_id,true);
		$criteria->compare('competency_id',$this->competency_id,true);
		$criteria->compare('competency_group_id',$this->competency_group_id,true);
		if (self::$fileUploadField == '1')
		{
			$criteria->compare('fs_file_id', $this->fs_file_id, true);
		}
		$criteria->compare('level_id',$this->level_id,true);
		$criteria->compare('result',$this->result,true);
		$criteria->compare('order',$this->order,true);
		$criteria->compare('note',$this->note,true);
		$criteria->compare('valid_from',$this->valid_from,true);
		$criteria->compare('valid_to',$this->valid_to,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_by',$this->created_by,true);
		$criteria->compare('created_on',$this->created_on,true);
		$criteria->compare('modified_by',$this->modified_by,true);
		$criteria->compare('modified_on',$this->modified_on,true);

		return new CActiveDataProvider($this, [
			'criteria'=>$criteria,
		]);
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return EmployeeCompetency the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function beforeValidate()
    {
        if ((int)$this->competencyOrderParams['show_order'] === 1
            && $this->order != ($this->getOldAttributes()['order'] ?? ''))
        {
            if ($this->isPriorityCompetencyFromContract) {
                return parent::beforeValidate();
            }

            if ($this->order == 1 && ((int)$this->competencyOrderParams['first_order_changeable'] === 0)) {
                $this->addGenericError("cannot_modify_primary_competency");
                return parent::beforeValidate();
            }

            $sql = "
                      SELECT 1
                        FROM employee_competency ecomp
                      WHERE
                            ecomp.employee_contract_id = '{$this->employee_contract_id}'
                       AND ecomp.status = " . self::$publishedStatus . "
                       AND ecomp.valid_from < '" . ($this->valid_to ?? App::getSetting('defaultEnd')) . "'
                       AND ecomp.valid_to   > '" . ($this->valid_from ?? App::getSetting('defaultStart')) . "'
                       AND ecomp.order = {$this->order}
                ";

            if (dbFetchValue($sql) && ((int)$this->competencyOrderParams['order_number_unique'] === 1))
            {
                $this->addGenericError("order_number_is_already_used", ["attribute" => $this->order]);
            }
        }

        return parent::beforeValidate();
    }

	public static function getCompetencyByEmployeeContractId($ecId, $filter, $group_select = null) : array
	{
	    $filter_competency_group = "";
		$filter_competency_name = "";

	    if ($group_select !== null && $group_select !== "") {
            $filter_competency_group = "AND comp.`competency_group_id` = '" . $group_select . "'";
        }

		if ($filter !== null && $filter !== "") {
            $filter_competency_name = "AND comp.`competency_name` LIKE '%{$filter}%'";
        }

		$sql = "
			SELECT
				comp.competency_id,
				comp.competency_name,
				ecomp.level_id AS competency_level,
				comp.`competency_group_id` AS competency_group_id,
				cg.`competency_group_name`,
				ecomp.valid_to,
				ecomp.`status`
			FROM
			    employee_competency ecomp
			LEFT JOIN
			        competency comp ON
					comp.competency_id = ecomp.competency_id
				AND comp.status = " . self::$publishedStatus . "
			LEFT JOIN `competency_group` cg ON
					cg.`competency_group_id` = comp.`competency_group_id`
				AND cg.`status` = " . self::$publishedStatus . "
			WHERE
					ecomp.employee_contract_id = '{$ecId}'
				AND ecomp.status = " . self::$publishedStatus . "
				AND CURDATE() BETWEEN ecomp.`valid_from` AND IFNULL(ecomp.`valid_to`, '" . App::getSetting('defaultEnd') . "')
				" . $filter_competency_name . "
				" . $filter_competency_group . "
				AND comp.`competency_id` IS NOT NULL
			ORDER BY
			    comp.competency_name
		";

		return dbFetchAll($sql);
	}

	public static function getDraftCompetencyByEmployeeContractId($ecId, $filter, $group_select = null) : array
	{
	    $filter_competency_group = "";
		$filter_competency_name = "";

	    if ($group_select !== null && $group_select !== "") {
            $filter_competency_group = "AND comp.`competency_group_id` = '" . $group_select . "'";
        }

		if ($filter !== null && $filter !== "") {
            $filter_competency_name = "AND comp.`competency_name` LIKE '%{$filter}%'";
        }

		$sql = "
			SELECT
				comp.competency_id,
				comp.competency_name,
				ecomp.level_id AS competency_level,
				comp.`competency_group_id` AS competency_group_id,
				ecomp.valid_to,
				ecomp.`status`
			FROM
			    employee_competency ecomp
			LEFT JOIN
			        competency comp ON
					comp.competency_id = ecomp.competency_id
				AND comp.status = " . self::$publishedStatus . "
			WHERE
					ecomp.employee_contract_id = '{$ecId}'
				AND ecomp.status = " . self::$draftStatus . "
				AND CURDATE() BETWEEN ecomp.`valid_from` AND IFNULL(ecomp.`valid_to`, '" . App::getSetting('defaultEnd') . "')
				" . $filter_competency_name . "
				" . $filter_competency_group . "
				AND comp.`competency_id` IS NOT NULL
			ORDER BY
			    comp.competency_name
		";

		return dbFetchAll($sql);
	}

    public static function getMaxOrderNumberByEmployeeContractId($ecId)
    {
        $sql = "
              SELECT MAX(ecomp.order)
                FROM employee_competency ecomp
              WHERE
					ecomp.employee_contract_id = '{$ecId}'
			   AND ecomp.status = " . self::$publishedStatus . "
			   AND CURDATE() between ecomp.valid_from AND ecomp.valid_to
        ";

        return dbFetchValue($sql);
    }

    /**
     * @return false
     */
    public function getIsPriorityCompetencyFromContract(): bool
    {
        return $this->isPriorityCompetencyFromContract;
    }

    /**
     * @param false $isPriorityCompetencyFromContract
     */
    public function setIsPriorityCompetencyFromContract(bool $isPriorityCompetencyFromContract): void
    {
        $this->isPriorityCompetencyFromContract = $isPriorityCompetencyFromContract;
    }
}

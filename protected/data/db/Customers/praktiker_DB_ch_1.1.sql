-- # RELEASE 1.1

UPDATE `_sql_version` SET `major_minor` = '1.1' WHERE `module` = 'c_praktiker';

UPDATE `_sql_version` SET `revision`=1, `updated_on`=NOW() WHERE `module` = 'c_praktiker';

-- VERSION -1--2025-07-10-09:00-----------------------------------------------------------

UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [1]}' WHERE template_rule_id=1;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [4]}' WHERE template_rule_id=3;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [6]}' WHERE template_rule_id=5;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [1]}' WHERE template_rule_id=7;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [1]}' WHERE template_rule_id=8;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [1]}' WHERE template_rule_id=9;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [1]}' WHERE template_rule_id=10;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [1]}' WHERE template_rule_id=11;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [4, 6]}' WHERE template_rule_id=17;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [4, 6]}' WHERE template_rule_id=18;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [4, 6]}' WHERE template_rule_id=19;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [4, 6]}' WHERE template_rule_id=20;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [4, 6]}' WHERE template_rule_id=21;
UPDATE scheduling_assistant_template_rule
  SET template_event_ids='{"shiftgroup_id": [4, 6]}' WHERE template_rule_id=22;

DELETE FROM scheduling_assistant_rules WHERE rule_id='expected-worktime-in-frames-to-optimal-worktime';
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "921feb2c9c3c8cc46e1d229940f73c4e", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Áruházvezetés', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "a002fe603613e4b7991e34165c9b94be", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Központ_6órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "7fb4e241c600613937513b551aa46745", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Központ_8órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "5473dee63e2feac67c49e702f959a12b", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Központ_7órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "6f79aa2f58701e166efb91348fb147c1", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Központ_5órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "e9af93281ee387b8df71b2abdb1fc6e7", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Központ_4órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "e5a5501e09d585642591f1efb76f2903", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: KAM_8órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "aefd5869e82cb008ece4c597db86084b", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: KAM_7órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "b56778fd58819055887fb3712d150abe", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: KAM_6órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "d5da89ca2901168c45fe560def5b647a", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: KAM_5órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "31d8452f8c04924f46392d167418c65a", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: KAM_4órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "f6f68c3a8b6b2243176cec2ca277971f", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Áruház_8órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "9df19858f9fe9fb9ffdbfc129b0cbaae", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Áruház_4órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "466ff07f610a4b325c8d81535d20656e", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Áruház_5órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "d791e0b9346a1e9db0957cc2fdb08130", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Áruház_6órás', 2, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_rules (rule_id, rule_type, rule_setting, coeff, note, status, created_by, created_on) VALUES ('expected-worktime-in-frames-to-optimal-worktime', 'constraint', '{"workgroup_id": "68d6d4ec1e321d4e8f76aca6c32e2f8f", "tolerance_sec": 36000}', 100, 'Optimális törvényi munkaidő betartása: Áruház_7órás', 2, 'scheduling-assistant', NOW());

UPDATE `_sql_version` SET `revision`=2, `updated_on`=NOW() WHERE `module` = 'c_praktiker';

-- VERSION -2--2025-07-10-10:00-----------------------------------------------------------

INSERT INTO competency (row_id, competency_id, competency_name, status, created_by, created_on)
VALUES (13, '13', 'Teruletvezeto', 2, 'scheduling-assistant', NOW());
INSERT INTO competency (row_id, competency_id, competency_name, status, created_by, created_on)
VALUES (14, '14', 'Kasszaterulet vezeto', 2, 'scheduling-assistant', NOW());

# Competency 13
# kiemelt teruletvezeto 40001134
# teruletvezeto 40001137
INSERT INTO employee_position_to_competency_map (row_id, employee_position_id, competency_id, status, created_by, created_on) VALUES (7, '40001134', '13', 2, 'scheduling-assistant', NOW());
INSERT INTO employee_position_to_competency_map (row_id, employee_position_id, competency_id, status, created_by, created_on) VALUES (8, '40001137', '13', 2, 'scheduling-assistant', NOW());

# Competency 14
# Kasszafelugyelo 40001126
# Kasszaterulet vezeto 40001127
# Kiemelt Kasszaterulet vezeto 40001133
INSERT INTO employee_position_to_competency_map (row_id, employee_position_id, competency_id, status, created_by, created_on) VALUES (9, '40001126', '14', 2, 'scheduling-assistant', NOW());
INSERT INTO employee_position_to_competency_map (row_id, employee_position_id, competency_id, status, created_by, created_on) VALUES (10, '40001127', '14', 2, 'scheduling-assistant', NOW());
INSERT INTO employee_position_to_competency_map (row_id, employee_position_id, competency_id, status, created_by, created_on) VALUES (11, '40001133', '14', 2, 'scheduling-assistant', NOW());

# Competency 13 -> ertekesitesi terulet (3, 4) -> area_manager_min
# events: 3 -> 4, 5, 6, 7
# events: 4 -> 8, 9
INSERT INTO scheduling_assistant_template_rule (row_id, template_id, template_event_ids, template_rule_id, name,
                                                rule_id, rule_type, rule_settings, coeff, status, valid_from, valid_to,
                                                note, created_by, created_on)
VALUES (29, '3', '{
  "shiftgroup_id": [
    4,
    5,
    6,
    7
  ]
}', '29', 'Area manager', 'shiftgroup-rule-with-competency', 'constraint', '{
  "minimum": "$area_manager_min:int:req$",
  "competency_id": "13",
  "shiftgroup_id": "$shiftgroup_id:string:auto$",
  "workgroup_ids": [
    "31d8452f8c04924f46392d167418c65a",
    "466ff07f610a4b325c8d81535d20656e",
    "5473dee63e2feac67c49e702f959a12b",
    "68d6d4ec1e321d4e8f76aca6c32e2f8f",
    "6f79aa2f58701e166efb91348fb147c1",
    "7fb4e241c600613937513b551aa46745",
    "921feb2c9c3c8cc46e1d229940f73c4e",
    "9df19858f9fe9fb9ffdbfc129b0cbaae",
    "a002fe603613e4b7991e34165c9b94be",
    "aefd5869e82cb008ece4c597db86084b",
    "b56778fd58819055887fb3712d150abe",
    "d5da89ca2901168c45fe560def5b647a",
    "d791e0b9346a1e9db0957cc2fdb08130",
    "e5a5501e09d585642591f1efb76f2903",
    "e9af93281ee387b8df71b2abdb1fc6e7",
    "f6f68c3a8b6b2243176cec2ca277971f"
  ]
}', 1000, 2, '1900-01-01', '2038-01-01', null, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_template_rule (row_id, template_id, template_event_ids, template_rule_id, name,
                                                rule_id, rule_type, rule_settings, coeff, status, valid_from, valid_to,
                                                note, created_by, created_on)
VALUES (30, '4', '{
  "shiftgroup_id": [
    8,
    9
  ]
}', '30', 'Area manager', 'shiftgroup-rule-with-competency', 'constraint', '{
  "minimum": "$area_manager_min:int:req$",
  "competency_id": "13",
  "shiftgroup_id": "$shiftgroup_id:string:auto$",
  "workgroup_ids": [
    "31d8452f8c04924f46392d167418c65a",
    "466ff07f610a4b325c8d81535d20656e",
    "5473dee63e2feac67c49e702f959a12b",
    "68d6d4ec1e321d4e8f76aca6c32e2f8f",
    "6f79aa2f58701e166efb91348fb147c1",
    "7fb4e241c600613937513b551aa46745",
    "921feb2c9c3c8cc46e1d229940f73c4e",
    "9df19858f9fe9fb9ffdbfc129b0cbaae",
    "a002fe603613e4b7991e34165c9b94be",
    "aefd5869e82cb008ece4c597db86084b",
    "b56778fd58819055887fb3712d150abe",
    "d5da89ca2901168c45fe560def5b647a",
    "d791e0b9346a1e9db0957cc2fdb08130",
    "e5a5501e09d585642591f1efb76f2903",
    "e9af93281ee387b8df71b2abdb1fc6e7",
    "f6f68c3a8b6b2243176cec2ca277971f"
  ]
}', 1000, 2, '1900-01-01', '2038-01-01', null, 'scheduling-assistant', NOW());

# Competency 14 -> normal (1, 2) -> cashier_manager_min
# events: 1 -> 1, 2
# events: 2 -> 3
INSERT INTO scheduling_assistant_template_rule (row_id, template_id, template_event_ids, template_rule_id, name,
                                                rule_id, rule_type, rule_settings, coeff, status, valid_from, valid_to,
                                                note, created_by, created_on)
VALUES (31, '1', '{
  "shiftgroup_id": [
    1,
    2
  ]
}', '31', 'Cashier manager', 'shiftgroup-rule-with-competency', 'constraint', '{
  "minimum": "$cashier_manager_min:int:req$",
  "competency_id": "14",
  "shiftgroup_id": "$shiftgroup_id:string:auto$",
  "workgroup_ids": [
    "31d8452f8c04924f46392d167418c65a",
    "466ff07f610a4b325c8d81535d20656e",
    "5473dee63e2feac67c49e702f959a12b",
    "68d6d4ec1e321d4e8f76aca6c32e2f8f",
    "6f79aa2f58701e166efb91348fb147c1",
    "7fb4e241c600613937513b551aa46745",
    "921feb2c9c3c8cc46e1d229940f73c4e",
    "9df19858f9fe9fb9ffdbfc129b0cbaae",
    "a002fe603613e4b7991e34165c9b94be",
    "aefd5869e82cb008ece4c597db86084b",
    "b56778fd58819055887fb3712d150abe",
    "d5da89ca2901168c45fe560def5b647a",
    "d791e0b9346a1e9db0957cc2fdb08130",
    "e5a5501e09d585642591f1efb76f2903",
    "e9af93281ee387b8df71b2abdb1fc6e7",
    "f6f68c3a8b6b2243176cec2ca277971f"
  ]
}', 1000, 2, '1900-01-01', '2038-01-01', null, 'scheduling-assistant', NOW());
INSERT INTO scheduling_assistant_template_rule (row_id, template_id, template_event_ids, template_rule_id, name,
                                                rule_id, rule_type, rule_settings, coeff, status, valid_from, valid_to,
                                                note, created_by, created_on)
VALUES (32, '2', '{
  "shiftgroup_id": [
    3
  ]
}', '32', 'Cashier manager', 'shiftgroup-rule-with-competency', 'constraint', '{
  "minimum": "$cashier_manager_min:int:req$",
  "competency_id": "14",
  "shiftgroup_id": "$shiftgroup_id:string:auto$",
  "workgroup_ids": [
    "31d8452f8c04924f46392d167418c65a",
    "466ff07f610a4b325c8d81535d20656e",
    "5473dee63e2feac67c49e702f959a12b",
    "68d6d4ec1e321d4e8f76aca6c32e2f8f",
    "6f79aa2f58701e166efb91348fb147c1",
    "7fb4e241c600613937513b551aa46745",
    "921feb2c9c3c8cc46e1d229940f73c4e",
    "9df19858f9fe9fb9ffdbfc129b0cbaae",
    "a002fe603613e4b7991e34165c9b94be",
    "aefd5869e82cb008ece4c597db86084b",
    "b56778fd58819055887fb3712d150abe",
    "d5da89ca2901168c45fe560def5b647a",
    "d791e0b9346a1e9db0957cc2fdb08130",
    "e5a5501e09d585642591f1efb76f2903",
    "e9af93281ee387b8df71b2abdb1fc6e7",
    "f6f68c3a8b6b2243176cec2ca277971f"
  ]
}', 1000, 2, '1900-01-01', '2038-01-01', null, 'scheduling-assistant', NOW());

UPDATE scheduling_assistant_template SET settings = '{"validations": [["shift_begin_time", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["shift_begin_time", "type", {"type": "time", "timeFormat": "h:mm"}, {"dictionaryId": "field_type_is_not_time", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["shift_end_time", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["shift_end_time", "type", {"type": "time", "timeFormat": "h:mm"}, {"dictionaryId": "field_type_is_not_time", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["minimum", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["minimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["optimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["maximum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3312_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3313_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3324_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["fire_protection_2_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["fire_protection_3_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["cashier_manager_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}]], "variableOrder": ["shift_begin_time", "shift_end_time", "minimum", "optimum", "maximum", "forklift_driver_3312_min", "forklift_driver_3313_min", "forklift_driver_3324_min", "fire_protection_2_min", "fire_protection_3_min", "cashier_manager_min"]}', status = 2, note = null, created_by = '6acd9683761b153750db382c1c3694f6', created_on = '2024-12-26 19:54:01', modified_by = null, modified_on = null WHERE row_id = 1;
UPDATE scheduling_assistant_template SET settings = '{"validations": [["shift_begin_time", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["shift_begin_time", "type", {"type": "time", "timeFormat": "h:mm"}, {"dictionaryId": "field_type_is_not_time", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["shift_end_time", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["shift_end_time", "type", {"type": "time", "timeFormat": "h:mm"}, {"dictionaryId": "field_type_is_not_time", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["minimum", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["minimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["optimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["maximum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3312_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3313_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3324_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["fire_protection_2_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["fire_protection_3_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["cashier_manager_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}]], "variableOrder": ["shift_begin_time", "shift_end_time", "minimum", "optimum", "maximum", "forklift_driver_3312_min", "forklift_driver_3313_min", "forklift_driver_3324_min", "fire_protection_2_min", "fire_protection_3_min", "cashier_manager_min"]}', status = 2, note = null, created_by = '6acd9683761b153750db382c1c3694f6', created_on = '2024-12-26 19:54:01', modified_by = null, modified_on = null WHERE row_id = 2;
UPDATE scheduling_assistant_template SET settings = '{"validations": [["shift_begin_time", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["shift_begin_time", "type", {"type": "time", "timeFormat": "h:mm"}, {"dictionaryId": "field_type_is_not_time", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["shift_end_time", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["shift_end_time", "type", {"type": "time", "timeFormat": "h:mm"}, {"dictionaryId": "field_type_is_not_time", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["logistic_minimum", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["logistic_minimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["logistic_optimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["logistic_maximum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["sales_minimum", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["sales_minimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["sales_optimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["sales_maximum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3312_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3313_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3324_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["fire_protection_2_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["fire_protection_3_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["plant_protection_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["area_manager_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}]], "variableOrder": ["shift_begin_time", "shift_end_time", "logistic_minimum", "logistic_optimum", "logistic_maximum", "sales_minimum", "sales_optimum", "sales_maximum", "forklift_driver_3312_min", "forklift_driver_3313_min", "forklift_driver_3324_min", "fire_protection_2_min", "fire_protection_3_min", "plant_protection_min", "area_manager_min"]}', modified_by = 'scheduling-assistant', modified_on = NOW() WHERE row_id = 3;
UPDATE scheduling_assistant_template SET settings = '{"validations": [["shift_begin_time", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["shift_begin_time", "type", {"type": "time", "timeFormat": "h:mm"}, {"dictionaryId": "field_type_is_not_time", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["shift_end_time", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["shift_end_time", "type", {"type": "time", "timeFormat": "h:mm"}, {"dictionaryId": "field_type_is_not_time", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["logistic_minimum", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["logistic_minimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["logistic_optimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["logistic_maximum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["sales_minimum", "required", {}, {"dictionaryId": "error_field_required", "dictionaryModule": "ttwa-base"}], ["sales_minimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["sales_optimum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["sales_maximum", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3312_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3313_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["forklift_driver_3324_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["fire_protection_2_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["fire_protection_3_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["plant_protection_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}], ["area_manager_min", "type", {"type": "integer"}, {"dictionaryId": "field_type_is_not_integer", "dictionaryModule": "ttwa-SchedulingAssistant"}]], "variableOrder": ["shift_begin_time", "shift_end_time", "logistic_minimum", "logistic_optimum", "logistic_maximum", "sales_minimum", "sales_optimum", "sales_maximum", "forklift_driver_3312_min", "forklift_driver_3313_min", "forklift_driver_3324_min", "fire_protection_2_min", "fire_protection_3_min", "plant_protection_min", "area_manager_min"]}', modified_by = 'scheduling-assistant', modified_on = NOW() WHERE row_id = 4;

INSERT INTO dictionary (lang, module, dict_id, dict_value, component, valid)
VALUES ('hu', 'ttwa-SchedulingAssistant', 'cashier_manager_min', 'Kasszafelügyelő minimum', null, 1);
INSERT INTO dictionary (lang, module, dict_id, dict_value, component, valid)
VALUES ('en', 'ttwa-SchedulingAssistant', 'cashier_manager_min', 'Cashier minimum', null, 1);
INSERT INTO dictionary (lang, module, dict_id, dict_value, component, valid)
VALUES ('hu', 'ttwa-SchedulingAssistant', 'area_manager_min', 'Területvezető minimum', null, 1);
INSERT INTO dictionary (lang, module, dict_id, dict_value, component, valid)
VALUES ('en', 'ttwa-SchedulingAssistant', 'area_manager_min', 'Area manager minimum', null, 1);

UPDATE `_sql_version` SET `revision`=3, `updated_on`=NOW() WHERE `module` = 'c_praktiker';

-- VERSION -3--2025-07-15-10:00-----------------------------------------------------------

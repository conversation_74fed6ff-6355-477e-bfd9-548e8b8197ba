<?php

declare(strict_types=1);

include_once("../vendor/flow-php/etl/src/Flow/ETL/DSL/functions.php");
include_once("../vendor/flow-php/array-dot/src/Flow/ArrayDot/array_dot.php");

use Components\Core\Command\AbstractUserSessionCommand;
use Components\Core\Provider\GlobalIdentifierProvider;
use Components\NexonApi\Enum\ConnectEnum;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;
use Components\NexonApi\Enum\NexonAPIExtratorEnum;
use Components\NexonApi\Enum\NexonFieldsEnum;
use Components\NexonApi\ETL\Extractor\NexonAPIExtractor;
use Components\NexonApi\ETL\Loader\ModelsInCacheToDbLoader;
use Components\NexonApi\ETL\Transformer\ArrayValueMathOperationTransformer;
use Components\NexonApi\ETL\Transformer\BaseAbsenceTypeMappingCacheTransformer;
use Components\NexonApi\ETL\Transformer\EmployeeContractsAndAbsenceMoreLevelCreateDescriptorTransformer;
use Components\NexonApi\ETL\Transformer\EmployeeContractDescriptorToModelsTransformer;
use Components\NexonApi\ETL\Transformer\EmployeeBaseAbsenceDescriptorToModelsTransformer;
use Components\NexonApi\ETL\Transformer\EmployeeExtCreateDescriptorTransformer;
use Components\NexonApi\ETL\Transformer\EmployeeExtDescriptorToModelsTransformer;
use Components\NexonApi\ETL\Transformer\EmployeePayrollUnitCog1Cog2Cog3CreateDescriptorTransformer;
use Components\NexonApi\ETL\Transformer\EmployeeDescriptorToModelsTransformer;
use Components\NexonApi\ETL\Transformer\IdentifierMappingCacheTransformer;
use Components\NexonApi\ETL\Transformer\NewUnitsToModelsTransformer;
use Components\NexonApi\ETL\Transformer\NewPayrollsToModelsTransformer;
use Components\NexonApi\ETL\Transformer\NewCompanyOrgGroupToModelsTransformer;
use Components\NexonApi\ETL\Transformer\NewEmployeePositionsToModelsTransformer;
use Components\NexonApi\ETL\Transformer\NewUsersToModelsTransformer;
use Components\NexonApi\ETL\Transformer\UsersLockUpdateTransformer;
use Components\NexonApi\ETL\Transformer\LoadGetOrgUnit3ToGroupTransformer;

use Flow\ETL\Cache\InMemoryCache;
use Flow\ETL\Config;
use Flow\ETL\Flow;

$composerAutoloder = include_once("../vendor/autoload.php");
Yang::registerAutoloader([$composerAutoloder, 'loadClass'], true);
Yang::loadComponentNamespaces('NexonApi');
Yang::loadComponentNamespaces('Employee');
Yang::loadComponentNamespaces('Core');
Yang::loadComponentNamespaces('ETL');

final class PennyNexonETLCommand extends AbstractUserSessionCommand
{
    public function actionRun(): int
    {
        ini_set('memory_limit', '8192M');
        $config = Config::builder()
            ->cache(new InMemoryCache())
            ->build();

        $identifierProvider = new GlobalIdentifierProvider();
        $processId = ($identifierProvider)(ConnectEnum::PROCESS_ID);
        $transaction = \Yang::app()->db->beginTransaction();
        try {
            Flow::setUp($config)
                ->extract(new NexonAPIExtractor((string)174))
                ->transform(
                    new ArrayValueMathOperationTransformer(
                        'positionFulfillmentsToPerson',
                        'WorkHoursInMinutes',
                        60,
                        'divide',
                        'WorkInHour'
                    )
                )
                ->transform(
                    new BaseAbsenceTypeMappingCacheTransformer(
                        EmployeeTablesFieldsEnum::BASE_ABSENCE_TYPE_MAPPING
                    )
                )
                ->transform(
                    new IdentifierMappingCacheTransformer()
                )
                ->transform(
                    new LoadGetOrgUnit3ToGroupTransformer(
                        'PayrollFromDbProvider',
                        NexonAPIExtratorEnum::PAYROLL_TABLE_COLUMNS,
                        NexonFieldsEnum::NEXON_LEVEL_2,
                        'getOrgUnits3Rows',
                        'newPayrolls',
                        'payrolls'
                    )
                )
                ->transform(
                    new LoadGetOrgUnit3ToGroupTransformer(
                        'UnitFromDbProvider',
                        NexonAPIExtratorEnum::UNIT_TABLE_COLUMNS,
                        NexonFieldsEnum::NEXON_LEVEL_3,
                        'getOrgUnits3Rows',
                        'newUnits',
                        'units'
                    )
                )
                ->transform(
                    new LoadGetOrgUnit3ToGroupTransformer(
                        'CompanyOrgGroup1FromDbProvider',
                        NexonAPIExtratorEnum::COMPANY_ORG_GROUP_TABLE_COLUMNS,
                        NexonFieldsEnum::NEXON_LEVEL_4,
                        'getOrgUnits3Rows',
                        'newCompanyOrgGroup1',
                        'companyOrgGroup1'
                    )
                )
                ->transform(
                    new LoadGetOrgUnit3ToGroupTransformer(
                        'CompanyOrgGroup2FromDbProvider',
                        NexonAPIExtratorEnum::COMPANY_ORG_GROUP_TABLE_COLUMNS,
                        NexonFieldsEnum::NEXON_LEVEL_5,
                        'getOrgUnits3Rows',
                        'newCompanyOrgGroup2',
                        'companyOrgGroup2'
                    )
                )
                ->transform(
                    new LoadGetOrgUnit3ToGroupTransformer(
                        'CompanyOrgGroup3FromDbProvider',
                        NexonAPIExtratorEnum::COMPANY_ORG_GROUP_TABLE_COLUMNS,
                        NexonFieldsEnum::NEXON_LEVEL_6,
                        'getOrgUnits3Rows',
                        'newCompanyOrgGroup3',
                        'companyOrgGroup3'
                    )
                )
                ->transform(
                    new EmployeePayrollUnitCog1Cog2Cog3CreateDescriptorTransformer(
                        'employeeDescriptors'
                    )
                )
                ->transform(
                    new EmployeeDescriptorToModelsTransformer(
                        'employeeDescriptors',
                        '',
                        'newEmployeeModelsCache'
                    )
                )
                ->transform(
                    new EmployeeContractsAndAbsenceMoreLevelCreateDescriptorTransformer(
                        'employeeContractDescriptors',
                    )
                )
                ->transform(
                    new EmployeeContractDescriptorToModelsTransformer(
                        'employeeContractDescriptors',
                        'newContractModelsCache',
                    )
                )
                ->transform(
                    new EmployeeBaseAbsenceDescriptorToModelsTransformer(
                        'employeeContractDescriptors',
                        'newEmployeeBaseAbsenceModelsCache',
                    )
                )
                ->transform(
                    new EmployeeExtCreateDescriptorTransformer(
                        'employeeExtDescriptors'
                    )
                )
                ->transform(
                    new EmployeeExtDescriptorToModelsTransformer(
                        'employeeExtDescriptors',
                        'newEmployeeExtModelsCache'
                    )
                )
                ->transform(
                    new NewEmployeePositionsToModelsTransformer(
                        'newEmployeePositions',
                        'newEmployeePositionsModels',
                    )
                )
                ->transform(
                    new NewPayrollsToModelsTransformer(
                        'newPayrolls',
                        'newPayrollsModel',
                    )
                )
                ->transform(
                    new NewUnitsToModelsTransformer(
                        'newUnits',
                        'newUnitsModel',
                    )
                )
                ->transform(
                    new NewCompanyOrgGroupToModelsTransformer(
                        1,
                        'newCompanyOrgGroup1',
                        'newCompanyOrgGroup1Model',
                    )
                )
                ->transform(
                    new NewCompanyOrgGroupToModelsTransformer(
                        2,
                        'newCompanyOrgGroup2',
                        'newCompanyOrgGroup2Model',
                    )
                )
                ->transform(
                    new NewCompanyOrgGroupToModelsTransformer(
                        3,
                        'newCompanyOrgGroup3',
                        'newCompanyOrgGroup3Model',
                    )
                )
                ->transform(
                    new NewUsersToModelsTransformer(
                        'users',
                        'newUsersModels',
                        2
                    )
                )
                ->transform(
                    new UsersLockUpdateTransformer(
                        'users'
                    )
                )
                ->write(
                    new ModelsInCacheToDbLoader(
                        [
                            'newEmployeeModelsCache',
                            'newEmployeeExtModelsCache',
                            'newContractModelsCache',
                            'newEmployeePositionsModels',
                            'newPayrollsModel',
                            'newUnitsModel',
                            'newCompanyOrgGroup1Model',
                            'newCompanyOrgGroup2Model',
                            'newCompanyOrgGroup3Model',
                            'newUsersModels',
                            'newEmployeeBaseAbsenceModelsCache',
                            'newApprover'
                        ],
                    )
                )
                ->run();
            $transaction->commit();
        } catch (\Exception $e) {
            \Yang::log(json_encode($e));
            $transaction->rollback();
        }
        return 0;
    }
}

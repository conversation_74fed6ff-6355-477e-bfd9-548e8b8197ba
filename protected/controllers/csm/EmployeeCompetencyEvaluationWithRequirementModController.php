<?php

class EmployeeCompetencyEvaluationWithRequirementModController extends Grid2Controller
{
	private $parentControllerId;
	private $publishedStatus = Status::PUBLISHED;
	private $draftStatus = Status::DRAFT;
	private $employeeEvaluationRowId;
	private $defaultEnd;
	private $userId;
	private $jobTaskTabId = "7"; //only dynamic tabs
	private $jobTaskFieldNameOnTab = "stufe"; //only dynamic tabs
	private $inProgStatus = Status::EVALUATION_NOT_IN_PROGRESS;
	private $fullname;
	private $useSimplifiedTrainingRequestMode;

	private $defaultExpectedLevelInSimplifiedTrainingRequestMode = 2;

	public function __construct()
	{
		parent::__construct("csm/employeeCompetencyEvaluationWithRequirementMod");

		$this->defaultEnd = App::getSetting("defaultEnd");
		$this->useSimplifiedTrainingRequestMode = App::getSetting("useSimplifiedTrainingRequestModeOnAssociationOfCompetencyLayout");

		$assetsPath = Yang::addAsset(Yang::getAlias('application.assets.csm'), false, -1, true);
		Yang::registerScriptFile($assetsPath.'/js/evaluationWithRequirementModActions.js');
		$this->parentControllerId = "csm/employeeCompetencyEvaluationWithRequirement";

		if (isset($_SESSION['employeeEvaluationRowId'])) {
			$this->employeeEvaluationRowId = $_SESSION['employeeEvaluationRowId'];
		}
		$this->fullname =  Yang::session('fullname');
	}

	/**
	 * Behúzza a a kártyaolvasást és a gombok lenyomását kezelő függvényeket tartalmazó js fájlt
	 * @param string $layout
	 * @param string $view
	 * @param array $params
	 */
	public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array())
	{
		Yang::setSessionValue('employeeEvaluationRowId', requestParam('selectedID'));
		$fullname = isset($_SESSION['employeeEvaluationRowId']) ? $this->getFullnameFromEEID($_SESSION['employeeEvaluationRowId']) : '';

		$params            = [
			"referer"              => requestParam('referer'),
			"id"                   => requestParam('id'),
			"evaluation_period"    => requestParam('evaluation_period'),
			"company"            	=> requestParam('company'),
			"payroll"              => requestParam('payroll'),
			"workgroup"           	=> requestParam('workgroup'),
			"unit" 				=> requestParam('unit'),
			"company_org_group3" 	=> requestParam('company_org_group3'),
			"employee_contract" 	=> requestParam('employee_contract'),
			"evaluation_status" 	=> requestParam('evaluation_status')
		];

		$url_params = "?" . http_build_query($params);
		$url        = "csm/employeeCompetencyEvaluationWithRequirement/index" . $url_params;
		Yang::setSessionValue('employeeCompetencyEvaluationWithRequirementUrl', $url);

		Yang::setSessionValue('referer', requestParam('referer'));
		Yang::setSessionValue('employee_competency_evaluation_with_requirement', requestParam('id'));

		$path = Yang::addAsset(Yang::getAlias('application.assets.csm'), false, -1, true);
		Yang::registerScriptFile($path . '/js/evaluationWithRequirementModActions.js');
		Yang::setSessionValue('fullname', $fullname);

		parent::actionIndex($layout, $view, $params);
	}

	public function actionGenStatusBar()
	{
		echo Dict::getValue("page_title_employee_evaluation") . " - " . $this->fullname;
	}

	protected function G2BInit()
	{
		parent::enableMultiGridMode();
		parent::setControllerPageTitleId("page_title_employee_evaluation");

		$this->LAGridRights->overrideInitRights("paging",			true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header",	true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select",			true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select",		false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move",		false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings",	false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details",			false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload",			true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting",		true, "dhtmlxGrid");

		if (!$this->checkIsLockedStatus($this->employeeEvaluationRowId) && App::hasRight($this->parentControllerId, "edit")) {
			$this->LAGridRights->overrideInitRights("modify", true, "dhtmlxGrid");
		}

		$this->LAGridDB->enableArrMode("dhtmlxGrid");
		$this->LAGridDB->setPrimaryKey("row_id",'dhtmlxGrid');

		parent::setGridProperty("splitColumnEnabled", true, "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 2, "dhtmlxGrid");

		parent::G2BInit();
	}

	public function dataArray($gridID, $filter)
	{
		if (App::hasRight("csm/employeeCompetencyEvaluationWithRequirement", "edit")) {
			$this->generateNewEmployeeEvaluationItemsData($this->employeeEvaluationRowId);
		}
		$result = $this->getEmployeeCompetencies($this->employeeEvaluationRowId);

		return $result;
	}

	protected function getEmployeeEvaluationDataByRowId($employeeEvaluationRowId)
	{
		$employeeEvaluationData = [];

		$SQL = "SELECT
					*
				FROM `employee_evaluation`
				WHERE
						`row_id` = '{$employeeEvaluationRowId}'
					AND `status` IN (" . $this->inProgStatus . "," . Status::EVALUATION_IN_PROGRESS .
			"," . Status::EVALUATION_LOCKED . ")
				";

		$result = dbFetchRow($SQL);

		$employeeEvaluationData['employeeContractId'] = $result['employee_contract_id'];
		$employeeEvaluationData['evaluationId'] = $result['evaluation_id'];
		$employeeEvaluationData['evaluationPeriodId'] = $result['evaluation_period_id'];
		
		return $employeeEvaluationData;
	}
	
	protected function checkIsLockedStatus($employeeEvaluationRowId)
	{
		$employeeEvaluationRow = EmployeeEvaluation::model()->findByPk($employeeEvaluationRowId);
		
		$currentStatus = (int) $employeeEvaluationRow->status;
		
		if ($currentStatus === Status::EVALUATION_LOCKED) {
			return true;
		} else {
			return false;
		}
	}
	
	protected function getEmployeeCompetencies($employeeEvaluationRowId)
	{
		$currentEvaluationData = $this->getEmployeeEvaluationDataByRowId($employeeEvaluationRowId);
		$ecId = $currentEvaluationData['employeeContractId'];
		
		$currentEvaluationId = $currentEvaluationData['evaluationId'];
		$employeeContractId = $currentEvaluationData['employeeContractId'];
		
		$lastEvaluationId = $this->getLastEvaluationIdbyEmployeeContractId($currentEvaluationId, $employeeContractId);
		$lastEvaluationIdCondition = ($lastEvaluationId !== null) ? $lastEvaluationId : "0";

		$groupHierarchy = explode(";", App::getSetting('group_hierarchy'));

		$extraSqls = AssociationOfCompetency::getSqlSelectAndJoin($groupHierarchy, "eperiod.evaluation_start_date", "eperiod.evaluation_end_date");

		$SQL = "
				SELECT
					eei.`row_id` AS row_id,
					eei.`evaluation_item_id` AS evaluation_item_id,
					eei.`level_id` AS new_level,
					eei.`positive_attibutes` AS positive_attributes,
					eei.`improvement_opportunities` AS improvement_opportunities,
					eei.`employee_note` AS employee_note,
					eei.`competency_id` AS competency_id,
					e.company_id AS company,
					" . $extraSqls['select'] . "
				FROM `employee_evaluation` ee
				LEFT JOIN `employee_evaluation_items` eei ON
						ee.`evaluation_id` = eei.`evaluation_id`
					AND eei.`status` = " . $this->publishedStatus . "
				LEFT JOIN `evaluation_period` eperiod ON
						ee.`evaluation_period_id` = eperiod.`evaluation_period_id`
					AND eperiod.`status` = " . $this->publishedStatus . "
				LEFT JOIN `employee_contract` ec ON
						ee.`employee_contract_id` = ec.`employee_contract_id`
					AND ec.`status` = " . $this->publishedStatus . "
					AND IFNULL(eperiod.`valid_to`, '{$this->defaultEnd}') >= ec.`valid_from`
					AND eperiod.`valid_from` <= IFNULL(ec.`valid_to`, '{$this->defaultEnd}')
					AND IFNULL(eperiod.`valid_to`, '{$this->defaultEnd}') >= ec.`ec_valid_from`
					AND eperiod.`valid_from` <= IFNULL(ec.`ec_valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee` e ON
						ec.`employee_id` = e.`employee_id`
					AND e.`status` = " . $this->publishedStatus . "
					AND IFNULL(eperiod.`valid_to`, '{$this->defaultEnd}') >= e.`valid_from`
					AND eperiod.`valid_from` <= IFNULL(e.`valid_to`, '{$this->defaultEnd}')
				" . $extraSqls['join'] . "
				WHERE 
						ee.`row_id` = '{$employeeEvaluationRowId}'
					AND ee.`status` IN (" . $this->inProgStatus . "," . Status::EVALUATION_IN_PROGRESS .
			"," . Status::EVALUATION_LOCKED . ")
				";

		$employeeData = dbFetchAll($SQL, 'competency_id');

		$competencies = AssociationOfCompetency::getHierarchyGroups($groupHierarchy, $employeeData);

		$existingcompetencies = EmployeeCompetency::getCompetencyByEmployeeContractId($ecId, "");

		if ($this->useSimplifiedTrainingRequestMode)
		{
			$competencies = [];
		}

		$competencyIdArray = array_merge(array_column($competencies, 'competency_id'), array_column($existingcompetencies, 'competency_id'));

		$trainingIds = TrainingLink::getTrainingIdsByCompetencyIds($competencyIdArray, true);
		$trainingRequests = TrainingRequest::getTrainingRequestByEcId($ecId);

		$result = [];
		$parentCompIds = [];
		foreach ($competencies as $id => $compData)
		{
			$parentCompIds[$compData['competency_id']] = $id;
			$result[$id] = $compData;
			$result[$id]['row_id'] = $employeeData[$compData['competency_id']]['row_id'];
			$result[$id]['evaluation_item_id'] = $employeeData[$compData['competency_id']]['evaluation_item_id'];
			$result[$id]['new_level'] = $employeeData[$compData['competency_id']]['new_level'];
			$result[$id]['positive_attributes'] = $employeeData[$compData['competency_id']]['positive_attributes'];
			$result[$id]['improvement_opportunities'] = $employeeData[$compData['competency_id']]['improvement_opportunities'];
			$result[$id]['employee_note'] = $employeeData[$compData['competency_id']]['employee_note'];
			$result[$id]['expected_level'] = $compData['competency_level'];
			$result[$id]['training_request'] = $trainingRequests[$compData['competency_id']]['training_id'];
		}

		foreach ($existingcompetencies as $comp)
		{
			if (array_key_exists($comp['competency_id'], $parentCompIds)) {
				$result[$parentCompIds[$comp['competency_id']]]['latest_level'] = $comp['competency_level'];
				if (!empty($result[$parentCompIds[$comp['competency_id']]]['latest_level'])
					&& (empty($result[$parentCompIds[$comp['competency_id']]]['new_level']) || $result[$parentCompIds[$comp['competency_id']]]['new_level'] === "0"))
				{
					$result[$parentCompIds[$comp['competency_id']]]['new_level'] = $comp['competency_level'];
					$this->saveLevel($result[$parentCompIds[$comp['competency_id']]]);
				}
				$result[$parentCompIds[$comp['competency_id']]]['valid_to'] = $comp['valid_to'];
				$result[$parentCompIds[$comp['competency_id']]]['training_request'] = $trainingRequests[$comp['competency_id']]['training_id'];
				if ($comp['competency_level'] >= $result[$parentCompIds[$comp['competency_id']]]['competency_level']) {
					$achievedCompetenciesNumber++;
				}
			}
			else if (!array_key_exists($comp['competency_id'], $parentCompIds) && $this->useSimplifiedTrainingRequestMode)
			{
				$id = $comp['competency_id'];

				$parentCompIds[$id] = $id;
				$result[$id] = $comp;
				$result[$id]['row_id'] = $employeeData[$id]['row_id'];
				$result[$id]['evaluation_item_id'] = $employeeData[$id]['evaluation_item_id'];
				$result[$id]['expected_level'] = $this->defaultExpectedLevelInSimplifiedTrainingRequestMode;
				$result[$id]['latest_level'] = $comp['competency_level'];
				$result[$id]['new_level'] = ($employeeData[$id]['new_level'] == 0) ? $comp['competency_level'] : $employeeData[$id]['new_level'];
				$result[$id]['positive_attributes'] = $employeeData[$id]['positive_attributes'];
				$result[$id]['improvement_opportunities'] = $employeeData[$id]['improvement_opportunities'];
				$result[$id]['employee_note'] = $employeeData[$id]['employee_note'];
				$result[$id]['training_request'] = $trainingRequests[$id]['training_id'];
			}
		}

		$_SESSION['tiptime'][userID()][$this->getControllerID()] = array_combine(array_column($result, 'row_id'), $result);

		return $result;
	}

	private function saveLevel($row)
	{
		dbExecute("UPDATE employee_evaluation_items SET level_id = '" . $row['new_level'] . "' WHERE row_id = '" . $row['row_id'] . "';");
	}

	protected function checkExistingEmployeeEvaluationItems($employeeEvaluationId)
	{
		$SQL = "
				SELECT 
					`row_id`
				FROM `employee_evaluation_items` ee
				WHERE
						`evaluation_id` = '{$employeeEvaluationId}'
					AND ee.`status` =  " . $this->publishedStatus . "
				";

		$existingEEIData = dbFetchColumn($SQL);

		return !empty($existingEEIData);
	}

	protected function getLastEvaluationIdbyEmployeeContractId($currentEmployeeEvaluationId, $employeeContractId)
	{
		$SQL = "
				SELECT
					`evaluation_id` as last_evaluation_id
				FROM `employee_evaluation` 
				WHERE 
						`employee_contract_id` = '{$employeeContractId}'
					AND `evaluation_id` != '{$currentEmployeeEvaluationId}'
					AND `status` = " . Status::EVALUATION_LOCKED . "
				ORDER BY `modified_on` DESC
				LIMIT 1
				";

		return dbFetchValue($SQL);
	}

	protected function getEcIdByEvaluationId($employeeEvaluationId)
	{
		$SQL = "
				SELECT
					ec.employee_contract_id
				FROM `employee_evaluation` ee
				LEFT JOIN `evaluation_period` eperiod ON
						ee.`evaluation_period_id` = eperiod.`evaluation_period_id`
					AND eperiod.`status` = " . $this->publishedStatus . "
				LEFT JOIN `employee_contract` ec ON
						ee.`employee_contract_id` = ec.`employee_contract_id`
					AND ec.`status` = " . $this->publishedStatus . "
					AND IFNULL(eperiod.`valid_to`, '{$this->defaultEnd}') >= ec.`valid_from`
					AND eperiod.`valid_from` <= IFNULL(ec.`valid_to`, '{$this->defaultEnd}')
					AND IFNULL(eperiod.`valid_to`, '{$this->defaultEnd}') >= ec.`ec_valid_from`
					AND eperiod.`valid_from` <= IFNULL(ec.`ec_valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee` e ON
						ec.`employee_id` = e.`employee_id`
					AND e.`status` = " . $this->publishedStatus . "
					AND IFNULL(eperiod.`valid_to`, '{$this->defaultEnd}') >= e.`valid_from`
					AND eperiod.`valid_from` <= IFNULL(e.`valid_to`, '{$this->defaultEnd}')
				WHERE 
						ee.`evaluation_id` = '{$employeeEvaluationId}'
					AND ee.`status` IN (" . $this->inProgStatus . "," . Status::EVALUATION_IN_PROGRESS .
			"," . Status::EVALUATION_LOCKED . ")
				";

		return dbFetchValue($SQL);
	}

	protected function generateNewEmployeeEvaluationItemsData($employeeEvaluationRowId)
	{
		$evaluationData = $this->getEmployeeEvaluationDataByRowId($employeeEvaluationRowId);
		$evaluationId = $evaluationData['evaluationId'];
		$existingEEIData = $this->checkExistingEmployeeEvaluationItems($evaluationId);

		if (!$existingEEIData && $evaluationId !== null)
		{
			$ecId = $this->getEcIdByEvaluationId($evaluationId);

			$employeeCompetencies = array_column(AssociationOfCompetency::getParentCompetenciesByEcId($ecId), 'competency_id');

			$existingCompetencyLevel = [];

			if ($this->useSimplifiedTrainingRequestMode)
			{
				$existingCompetencies = EmployeeCompetency::getCompetencyByEmployeeContractId($ecId, "");

				foreach ($existingCompetencies as $existingCompetency)
				{
					$existingCompetencyLevel[$existingCompetency['competency_id']] = $existingCompetency['competency_level'];
				}
				
				$existingCompetencyIds = array_column($existingCompetencies, 'competency_id');

				$checkExistingDraftCompetencies = $this->checkExistingDraftCompetencies($employeeCompetencies, $ecId);
				
				$employeeCompetenciesWithoutDraftCompetencies = array_values(array_diff($employeeCompetencies, $checkExistingDraftCompetencies));

				$employeeCompetencies = array_unique(array_merge($employeeCompetenciesWithoutDraftCompetencies, $existingCompetencyIds));
			}

			$data = [];
			
			for ($i = 0; $i < count($employeeCompetencies); $i++)
			{
				$competencyId = $employeeCompetencies[$i];
				
				if (!empty($competencyId)) {
					$data[] =
					[
						'evaluation_item_id' => md5(__CLASS__.date('YmdHis').$evaluationId.$competencyId),
						'evaluation_id' => $evaluationId,
						'competency_id' => $competencyId,
						'level_id' => array_key_exists($competencyId, $existingCompetencyLevel) ? $existingCompetencyLevel[$competencyId] : 0,
						'status' =>  Status::EVALUATION_IN_PROGRESS,
						'created_by' => userId(),
						'created_on' => date('Y-m-d H:i:s'),
					];
				}
			}
			$employeeEvaluationRow = EmployeeEvaluation::model()->findByPk($employeeEvaluationRowId);
			$employeeEvaluationRow->status = Status::EVALUATION_IN_PROGRESS;
			$employeeEvaluationRow->save();

			if (!empty($data)) dbMultiInsert('employee_evaluation_items', $data);
		}
	}

	public function columns()
	{
		$columns = [];

		$eeiRowId = requestParam('editPK');
		$trainingRequestSQL = "
			SELECT 
				training.`training_id` as id, 
				training.`training_name` as value
			FROM `training`
			LEFT JOIN training_link tl ON
					tl.training_id = training.training_id
				AND tl.status = {$this->publishedStatus}
			LEFT JOIN employee_evaluation_items eei ON
					eei.competency_id = tl.competency_id
				AND eei.status = {$this->publishedStatus}
			WHERE
					training.`status` = {$this->publishedStatus}
				AND CURDATE() BETWEEN training.`valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')";

		if(isset($eeiRowId)) {
			$trainingRequestSQL .= " 
			AND eei.row_id = '{$eeiRowId}'
			";
		}

		$trainingRequestSQL .= "ORDER BY training_name";

		$levelSql = "SELECT 
						`level_id` AS id, 
						`level_name` AS value 
					FROM `competency_levels`
					WHERE 
						`status` = " . $this->publishedStatus . "
					ORDER BY `level_order`";

		$row = $_SESSION['tiptime'][userID()][$this->getControllerID()][$eeiRowId];

		$columns["dhtmlxGrid"] = [
			'competency_group_name'		=> ['grid' => true, 'window' => false, 'col_type' => 'ro', 'align' => 'left', 'width' => '300'],
			'competency_name'			=> ['grid' => true, 'window' => false, 'col_type' => 'ro', 'align' => 'left', 'width' => '300'],
			'expected_level'			=> ['grid' => true, 'window' => false, 'col_type' => 'combo', 'align' => 'left', 'width' => '200',
				'options'	=> [
					'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'		=> $levelSql
				]],
			'latest_level'				=> ['grid' => true, 'window' => false, 'col_type' => 'combo', 'align' => 'left', 'width' => '200',
				'options'	=> [
					'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'		=> $levelSql
				]],

			'new_level'					=> [
				'grid'		=> true,
				'window' => true,
				'col_type'	=> 'combo',
				'align'		=> 'left',
				'width'		=> '200',
				'options'	=> [
					'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'		=> $levelSql,
				],
				'default_value'	=> $row['new_level'] ?? ''
			],

			'positive_attributes'		=> ['grid' => true, 'window' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '300', 'default_value' => $row['positive_attributes']],
			'improvement_opportunities'	=> ['grid' => true, 'window' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '300', 'default_value' => $row['improvement_opportunities']],
			'employee_note'				=> ['grid' => true, 'window' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '300', 'default_value' => $row['employee_note']],
			'training_request'			=> [
				'grid'		=> true,
				'col_type'	=> 'combo',
				'window' => true,
				'align'		=> 'center',
				'width'		=> '200',
				'options' => [
					'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	 		=> $trainingRequestSQL,
					'array'			=> ['id' => '', 'value' => ''],
				],
				'default_value'	=> $row['training_request'] ?? ''
			]
		];

		return $columns;
	}

	public function attributeLabels()
	{
		$attributeLabels = [];

		$attributeLabels["dhtmlxGrid"] = [
			'competency_group_name'		=> Dict::getValue("competency_group_name"),
			'competency_name'			=> Dict::getValue("competency_name"),
			'expected_level'			=> Dict::getValue("expected_level"),
			'latest_level'				=> Dict::getValue("latest_level"),
			'new_level'					=> Dict::getValue("new_level"),
			'positive_attributes'		=> Dict::getValue("positive_attributes"),
			'improvement_opportunities'	=> Dict::getValue("improvement_opportunities"),
			'employee_note'				=> Dict::getValue("employee_note"),
			'training_request'			=> Dict::getValue("training_request")
		];

		return $attributeLabels;
	}

	public function actionSave($data = [], $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null)
	{
		$this->layout = "//layouts/ajax";
		$form = requestParam('gridID');
		$data = requestParam("dialogInput_$form");
		$saved	= 1;
		$message = '';

		$currentEmployeeEvaluationData = $this->getEmployeeEvaluationDataByRowId($this->employeeEvaluationRowId);

		$newLevelId					= $data['new_level'];
		$positiveAttributes         = $data['positive_attributes'];
		$improvementOpportunities   = $data['improvement_opportunities'];
		$employeeNote               = $data['employee_note'];
		$trainingRequestId          = $data['training_request'];

		if ($_REQUEST['dialogMode'] == 1)
		{
			$new_eei = EmployeeEvaluationItems::model()->findByPk(requestParam('editPK'));
			$new_eei->level_id = ($newLevelId !== "") ? $newLevelId : null;
			$new_eei->positive_attibutes = ($positiveAttributes !== "") ? $positiveAttributes : $new_eei->positive_attibutes;
			$new_eei->improvement_opportunities = ($improvementOpportunities !== "") ?
				$improvementOpportunities :
				$new_eei->improvement_opportunities;
			$new_eei->employee_note = ($employeeNote !== "") ? $employeeNote : $new_eei->employee_note;
			$new_eei->save();

			if ($trainingRequestId !== "") {
				if (!$this->useSimplifiedTrainingRequestMode) {
					$courseId = $this->getCourseIdByTrainingAndEvaluationPeriodId($currentEmployeeEvaluationData['evaluationPeriodId'], $trainingRequestId);
				} else {
					$courseId = null;
				}
				
				$this->saveTrainingRequest(
					$currentEmployeeEvaluationData['evaluationPeriodId'],
					$currentEmployeeEvaluationData['employeeContractId'],
					$new_eei->competency_id,
					$trainingRequestId,
					$courseId
				);
			}
		}

		if ($saved) {
			$status = [
				'status'	=> 1,
			];
		} else {
			$status = [
				'status'	=> 0,
				'error'	=> $message,
			];
		}
		echo json_encode($status);
	}

	public function actionGetCompetencyLevelInfo()
	{
		$title = Dict::getValue("page_title_competency_level");
		$response = [];

		$SQL = "
			SELECT
				`level_order`,
				`level_name`,
				`note`
			FROM `competency_levels`
			WHERE
				`status` = {$this->publishedStatus}
			ORDER BY `level_order`
		";

		$results = dbFetchAll($SQL);

		if (!empty($results))
		{
			$response = [
				"title" 	=> $title,
				"status" 	=> 1,
				"results" 	=> $results
			];
		} else
		{
			$response = [
				"title" 	=> $title,
				"status" 	=> 0,
				"message" 	=> Dict::getValue("missingCompetencyLevelInfo")
			];
		}

		echo json_encode($response);
	}

	protected function getStatusButtons($gridID = null)
	{
		$actButtons = parent::getStatusButtons($gridID);

		$modButtons = [];

		if ($gridID === "dhtmlxGrid")
		{
			//Vissza az előző oldalra
			$modButtons[] = [
				"type"		=> "button",
				"id"		=> "backToPrevSite",
				"class"		=> "backToPrevSite",
				"name"		=> "backToPrevSite",
				"img"		=> "/images/status_icons/st_prev.png",
				"label"		=> Dict::getValue("evaluation_start_or_continue")
				//"onclick"	=> "backToPrevSite();",
			];

			$modButtons[] = [
				"type"		=> "button",
				"id"		=> "competencyLevelInfo",
				"class"		=> "competencyLevelInfo",
				"name"		=> "competencyLevelInfo",
				"img"		=> "/images/status_icons/st_question.png",
				"label"		=> Dict::getValue("competencyLevelInfo"),
				"onclick"	=> "showCompetencyLevelInfo();",
			];
		}

		$buttons = Yang::arrayMerge($modButtons, $actButtons);

		return $buttons;
	}

	private function getCourseIdByTrainingAndEvaluationPeriodId($evaluationPeriodId, $trainingId)
	{
		$courseId = null;

		$SQL = "
				SELECT DISTINCT
					tc.`course_id` as course_id
				FROM `calendar` cal
				LEFT JOIN `evaluation_period` eperiod ON
						eperiod.`status` = " . $this->publishedStatus . "
					AND cal.`date` BETWEEN eperiod.`valid_from` AND IFNULL(eperiod.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `training_course` tc ON
						tc.`status` = " . $this->publishedStatus . "
					AND cal.`date` BETWEEN tc.`valid_from` AND IFNULL(tc.`valid_to`, '{$this->defaultEnd}')
				WHERE
						eperiod.`evaluation_period_id` = '{$evaluationPeriodId}'
					AND tc.`training_id` = '{$trainingId}'
				";

		$result = dbFetchRow($SQL);

		if (count($result) > 0) {
			$courseId = $result['course_id'];
		}

		return $courseId;
	}

	private function saveTrainingRequest($evaluationPeriodId, $employeeContractId, $competencyId, $trainingId, $courseId)
	{
		$action = 0; //0: no changes, 1: change existing data, 2: insert new request

		$trainingRequest = TrainingRequest::model()->findByAttributes(
			[
				'evaluation_period_id'	=>	$evaluationPeriodId,
				'employee_contract_id'	=>	$employeeContractId,
				'competency_id'			=>	$competencyId
			],
			"status IN (" . Status::DRAFT . "," . $this->publishedStatus . "," . Status::LOCKED . ")"
		);

		if (empty($trainingRequest))
		{
			$trainingRequest = new TrainingRequest;
			$trainingRequest->evaluation_period_id = $evaluationPeriodId;
			$trainingRequest->employee_contract_id = $employeeContractId;
			$trainingRequest->competency_id = $competencyId;
			$trainingRequest->training_id = $trainingId;
			$trainingRequest->training_course_id = $courseId;
			$trainingRequest->status = Status::DRAFT;
			$trainingRequest->save();

			return $action = 2;
		}
		else
		{
			if ($trainingRequest->training_id !== $trainingId || $trainingRequest->training_course_id !== $courseId)
			{
				$trainingRequest->training_id = $trainingId;
				$trainingRequest->training_course_id = $courseId;
				$trainingRequest->save();

				return $action = 1;
			} else {
				return $action = 0;
			}
		}
	}

	private function getFullnameFromEEID($eeID)
	{
		$sql = "SELECT DISTINCT CONCAT(e.last_name, ' ', e.first_name)
				FROM employee_evaluation ee
				LEFT JOIN evaluation_period ep ON
						ee.evaluation_period_id = ep.evaluation_period_id
					AND ep.status = {$this->publishedStatus}
				LEFT JOIN employee_contract ec ON
						ee.employee_contract_id = ec.employee_contract_id
					AND ec.status = {$this->publishedStatus}
					AND ec.valid_from <= IFNULL(ep.valid_to, '{$this->defaultEnd}')
					AND ec.ec_valid_from <= IFNULL(ep.valid_to, '{$this->defaultEnd}')
					AND ep.valid_from <= IFNULL(ec.valid_to, '{$this->defaultEnd}')
					AND ep.valid_from <= IFNULL(ec.ec_valid_to, '{$this->defaultEnd}')
				LEFT JOIN employee e ON
						e.employee_id = ec.employee_id
					AND ec.status = {$this->publishedStatus}
					AND e.valid_from <= IFNULL(ep.valid_to, '{$this->defaultEnd}')
					AND ep.valid_from <= IFNULL(e.valid_to, '{$this->defaultEnd}')
				WHERE ee.row_id = {$eeID}
		";
		return dbFetchValue($sql);
	}

	private function checkExistingDraftCompetencies(array $competency, string $ecId): array
	{
		$existingDraftCompetencies = [];

		$SQL = "
			SELECT
				tr.`competency_id`
			FROM `training_request` tr
			LEFT JOIN `employee_competency` ecomp ON 
					ecomp.`competency_id` = tr.`competency_id`
				AND ecomp.`employee_contract_id` = tr.`employee_contract_id`
				AND ecomp.`status` = {$this->publishedStatus}
				AND CURDATE() BETWEEN ecomp.`valid_from` AND IFNULL(ecomp.`valid_to`, '{$this->defaultEnd}')
			WHERE 
			    	tr.`status` = {$this->draftStatus}
				AND tr.`employee_contract_id` = '$ecId'
				AND tr.`competency_id` IN ('" . implode("','", $competency) . "') 
				AND tr.`row_id` IS NOT NULL AND ecomp.`row_id` IS NULL
		";
		
		$results = dbFetchAll($SQL);

		foreach ($results as $result) {
			$existingDraftCompetencies[] = $result['competency_id'];
		}

		return $existingDraftCompetencies;
	}

}